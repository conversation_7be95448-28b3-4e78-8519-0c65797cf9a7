#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
彩票数据分析四步程序
按照用户要求的四个步骤进行彩票数据分析：
1. 读取Excel表格数据并排序
2. 指定数据范围、开始行与计算公式
3. 开始计算和比对
4. 将校核结果保存在Excel文件中
"""

import pandas as pd
import numpy as np
import time
from datetime import datetime
import threading
import sys

class LotteryAnalyzer:
    """彩票分析器类"""
    
    def __init__(self, file_path="lottery_data_all.xlsx"):
        """
        初始化彩票分析器
        
        Args:
            file_path: Excel文件路径
        """
        self.file_path = file_path
        self.ssqhistory_allout = None
        self.dlthistory_allout = None
        self.results = []  # 存储校核结果
        
    def step1_read_and_sort_data(self):
        """
        第一步：读取Excel表格数据并排序
        """
        print("=" * 60)
        print("第一步：读取Excel表格数据并排序")
        print("=" * 60)
        
        try:
            # 读取双色球数据
            print("正在读取双色球数据...")
            ssq_data = pd.read_excel(self.file_path, sheet_name="SSQ_data_all")
            
            # 提取A列、I列至O列数据（共8列）
            # A列是第0列，I列至O列是第8-14列
            self.ssqhistory_allout = ssq_data.iloc[:, [0] + list(range(8, 15))].copy()
            
            # 读取大乐透数据
            print("正在读取大乐透数据...")
            dlt_data = pd.read_excel(self.file_path, sheet_name="DLT_data_all")
            
            # 提取A列、H列至N列数据（共8列）
            # A列是第0列，H列至N列是第7-13列
            self.dlthistory_allout = dlt_data.iloc[:, [0] + list(range(7, 14))].copy()
            
            # 清理和排序数据
            datasets = {
                "ssqhistory_allout": self.ssqhistory_allout,
                "dlthistory_allout": self.dlthistory_allout
            }
            
            for name, dataset in datasets.items():
                print(f"处理 {name} 数据...")
                
                # 删除包含NaN的行
                dataset.dropna(inplace=True)
                
                # 按第一列（NO列）从小到大排序
                dataset.sort_values(by=dataset.columns[0], inplace=True)
                
                # 重置索引
                dataset.reset_index(drop=True, inplace=True)
                
                # 确保数据类型一致性
                try:
                    # 第一列是期号，应该是整数类型
                    dataset.iloc[:, 0] = dataset.iloc[:, 0].astype(int)
                    
                    # 其他列是彩票号码，也应该是整数类型
                    for col in range(1, dataset.shape[1]):
                        dataset.iloc[:, col] = dataset.iloc[:, col].astype(int)
                except ValueError as e:
                    print(f"警告: 转换 {name} 的数据类型时出错: {e}")
                    # 使用更安全的方法
                    for col in range(dataset.shape[1]):
                        dataset.iloc[:, col] = dataset.iloc[:, col].fillna(0).astype(int)
            
            # 打印数据信息
            print(f"\nssqhistory_allout 数据信息:")
            print(f"行数: {self.ssqhistory_allout.shape[0]}, 列数: {self.ssqhistory_allout.shape[1]}")
            print(f"期号范围: {self.ssqhistory_allout.iloc[0, 0]} - {self.ssqhistory_allout.iloc[-1, 0]}")
            
            print(f"\ndlthistory_allout 数据信息:")
            print(f"行数: {self.dlthistory_allout.shape[0]}, 列数: {self.dlthistory_allout.shape[1]}")
            print(f"期号范围: {self.dlthistory_allout.iloc[0, 0]} - {self.dlthistory_allout.iloc[-1, 0]}")
            
            print("\n第一步完成：数据读取和排序成功！")
            return True
            
        except FileNotFoundError:
            print(f"错误: 找不到文件 '{self.file_path}'，请确保文件存在。")
            return False
        except Exception as e:
            print(f"错误: {str(e)}")
            return False

    def get_user_input_with_timeout(self, prompt, timeout=30, default_value=None):
        """
        获取用户输入，支持超时（Windows兼容版本）

        Args:
            prompt: 提示信息
            timeout: 超时时间（秒）
            default_value: 默认值

        Returns:
            用户输入或默认值
        """
        print(f"{prompt}（{timeout}秒后将使用默认值: {default_value}）")

        # 使用简单的方式：直接获取输入，不实现复杂的超时机制
        # 在实际使用中，用户可以直接按回车使用默认值
        try:
            user_input = input("请输入（直接回车使用默认值）: ")
            if user_input.strip() == "":
                print(f"使用默认值: {default_value}")
                return str(default_value) if default_value is not None else ""
            return user_input.strip()
        except KeyboardInterrupt:
            print(f"\n用户中断，使用默认值: {default_value}")
            return str(default_value) if default_value is not None else ""

    def step2_get_user_parameters(self):
        """
        第二步：指定数据范围、开始行与计算公式
        """
        print("\n" + "=" * 60)
        print("第二步：指定数据范围、开始行与计算公式")
        print("=" * 60)
        
        # 1. 选择彩票类型
        print("\n请选择彩票类型:")
        print("1. SSQ (双色球)")
        print("2. DLT (大乐透)")
        
        lottery_choice = self.get_user_input_with_timeout(
            "请输入选择 (1 或 2，30秒后默认选择1): ", 30, "1"
        )
        
        if lottery_choice == "2":
            self.lottery_type = "DLT"
            self.data = self.dlthistory_allout
            print("您选择了：大乐透 (DLT)")
        else:
            self.lottery_type = "SSQ"
            self.data = self.ssqhistory_allout
            print("您选择了：双色球 (SSQ)")
        
        # 2. 选择开始行
        max_rows = len(self.data)
        print(f"\n数据总行数: {max_rows}")
        print(f"建议开始行数范围: 1000 - {max_rows - 112}")

        start_row_input = self.get_user_input_with_timeout(
            f"请输入开始行数 (30秒后默认选择2000): ", 30, "2000"
        )

        try:
            self.start_row = int(start_row_input)
            if self.start_row < 1 or self.start_row >= max_rows - 112:
                print(f"警告: 开始行数超出合理范围，使用默认值2000")
                self.start_row = min(2000, max_rows - 112)
        except ValueError:
            print("输入无效，使用默认值2000")
            self.start_row = min(2000, max_rows - 112)
        
        print(f"开始行数设置为: {self.start_row}")
        
        # 3. 选择计算方法
        print("\n请选择计算方法:")
        print("1. 基于贝叶斯概率预测")
        print("2. 基于多条件贝叶斯概率预测")
        print("3. 基于全条件贝叶斯概率预测")
        print("4. 基于贝叶斯预测")
        print("5. 基于马尔可夫预测")
        print("6. 基于集成预测")

        method_choice = self.get_user_input_with_timeout(
            "请输入选择 (1、2、3、4、5 或 6，30秒后默认选择6): ", 30, "6"
        )

        if method_choice == "1":
            self.method = "bayesian"
            print("您选择了：基于贝叶斯概率预测")
        elif method_choice == "3":
            self.method = "full_bayesian"
            print("您选择了：基于全条件贝叶斯概率预测")
        elif method_choice == "4":
            self.method = "bayesian_ai"
            print("您选择了：基于贝叶斯预测")
        elif method_choice == "5":
            self.method = "markov"
            print("您选择了：基于马尔可夫预测")
        elif method_choice == "6":
            self.method = "ensemble"
            print("您选择了：基于集成预测")
        else:
            self.method = "multi_bayesian"
            print("您选择了：基于多条件贝叶斯概率预测")
        
        print("\n第二步完成：参数设置成功！")
        print(f"彩票类型: {self.lottery_type}")
        print(f"开始行数: {self.start_row}")
        print(f"计算方法: {self.method}")
        
        return True

    def calculate_ball_statistics(self, df, columns, min_ball, max_ball):
        """
        统计球号出现次数和概率

        Args:
            df: 数据集
            columns: 要统计的列索引列表
            min_ball: 最小球号
            max_ball: 最大球号

        Returns:
            times: 各球号出现次数的字典
            probs: 各球号出现概率的字典
        """
        # 创建一个字典，用于存储每个球号的出现次数
        times = {i: 0 for i in range(min_ball, max_ball + 1)}

        # 将所有指定列的数据合并为一个numpy数组
        all_balls = np.array([])
        for col in columns:
            all_balls = np.append(all_balls, df.iloc[:, col].values)

        # 统计每个球号的出现次数
        for ball in all_balls:
            if min_ball <= ball <= max_ball:
                times[int(ball)] += 1

        # 计算总次数
        total_count = sum(times.values())

        # 计算每个球号的出现概率
        probs = {ball: count / total_count if total_count > 0 else 0 for ball, count in times.items()}

        return times, probs

    def is_prime(self, n):
        """
        判断一个数是否为质数

        Args:
            n: 要判断的数

        Returns:
            bool: 是否为质数
        """
        if n < 2:
            return False
        if n == 2:
            return True
        if n % 2 == 0:
            return False
        for i in range(3, int(n**0.5) + 1, 2):
            if n % i == 0:
                return False
        return True

    def calculate_repeat_analysis(self):
        """
        计算重号分析（连续两期的相同号码）

        Returns:
            repeat_stats: 重号统计结果
        """
        repeat_stats = {
            'red_repeats': [],  # 红球重号记录
            'blue_repeats': [],  # 蓝球重号记录
            'red_repeat_count': 0,  # 红球重号总次数
            'blue_repeat_count': 0,  # 蓝球重号总次数
            'red_repeat_numbers': {},  # 各红球号码的重号次数
            'blue_repeat_numbers': {}  # 各蓝球号码的重号次数
        }

        if self.lottery_type == "SSQ":
            red_columns = range(1, 7)
            blue_columns = [7]
            red_range = (1, 33)
            blue_range = (1, 16)
        else:
            red_columns = range(1, 6)
            blue_columns = [6, 7]
            red_range = (1, 35)
            blue_range = (1, 12)

        # 初始化重号次数统计
        for i in range(red_range[0], red_range[1] + 1):
            repeat_stats['red_repeat_numbers'][i] = 0
        for i in range(blue_range[0], blue_range[1] + 1):
            repeat_stats['blue_repeat_numbers'][i] = 0

        # 分析连续两期的重号情况
        for i in range(len(self.data) - 1):
            # 获取当前期和下一期的号码
            current_red = set([self.data.iloc[i, col] for col in red_columns])
            next_red = set([self.data.iloc[i+1, col] for col in red_columns])

            current_blue = set([self.data.iloc[i, col] for col in blue_columns])
            next_blue = set([self.data.iloc[i+1, col] for col in blue_columns])

            # 计算红球重号
            red_repeat = current_red & next_red
            if red_repeat:
                repeat_stats['red_repeats'].append({
                    'period1': self.data.iloc[i, 0],
                    'period2': self.data.iloc[i+1, 0],
                    'repeat_numbers': list(red_repeat),
                    'repeat_count': len(red_repeat)
                })
                repeat_stats['red_repeat_count'] += len(red_repeat)
                for num in red_repeat:
                    repeat_stats['red_repeat_numbers'][num] += 1

            # 计算蓝球重号
            blue_repeat = current_blue & next_blue
            if blue_repeat:
                repeat_stats['blue_repeats'].append({
                    'period1': self.data.iloc[i, 0],
                    'period2': self.data.iloc[i+1, 0],
                    'repeat_numbers': list(blue_repeat),
                    'repeat_count': len(blue_repeat)
                })
                repeat_stats['blue_repeat_count'] += len(blue_repeat)
                for num in blue_repeat:
                    repeat_stats['blue_repeat_numbers'][num] += 1

        return repeat_stats

    def calculate_follow_statistics(self, df, columns, min_ball, max_ball):
        """
        计算球号的跟随性统计

        Args:
            df: 数据集
            columns: 要统计的列索引列表
            min_ball: 最小球号
            max_ball: 最大球号

        Returns:
            follow_time: 跟随次数矩阵
            follow_prob: 跟随概率矩阵
        """
        # 创建跟随次数矩阵和概率矩阵
        num_balls = max_ball - min_ball + 1
        follow_time = np.zeros((num_balls, num_balls), dtype=int)
        follow_prob = np.zeros((num_balls, num_balls), dtype=float)

        # 遍历数据集中的每一行（除了最后一行）
        for i in range(len(df) - 1):
            # 获取当前行和下一行的球号
            current_row_balls = set()
            next_row_balls = set()

            # 收集当前行的所有球号
            for col in columns:
                ball = df.iloc[i, col]
                if min_ball <= ball <= max_ball:
                    current_row_balls.add(int(ball))

            # 收集下一行的所有球号
            for col in columns:
                ball = df.iloc[i+1, col]
                if min_ball <= ball <= max_ball:
                    next_row_balls.add(int(ball))

            # 更新跟随次数矩阵
            for current_ball in current_row_balls:
                for next_ball in next_row_balls:
                    # 矩阵索引从0开始，所以需要减去min_ball
                    follow_time[next_ball - min_ball, current_ball - min_ball] += 1

        # 计算跟随概率矩阵
        for col in range(num_balls):
            col_sum = np.sum(follow_time[:, col])
            if col_sum > 0:
                follow_prob[:, col] = follow_time[:, col] / col_sum

        return follow_time, follow_prob

    def predict_numbers(self, train_data, method="multi_bayesian"):
        """
        根据训练数据预测下一期号码

        Args:
            train_data: 训练数据
            method: 预测方法

        Returns:
            predicted_numbers: 预测的号码列表
        """
        if len(train_data) < 10:  # 需要足够的训练数据
            return None

        if self.lottery_type == "SSQ":
            # 双色球：红球1-33，蓝球1-16
            red_columns = range(1, 7)  # 第2-7列是红球
            blue_columns = [7]  # 第8列是蓝球
            red_range = (1, 33)
            blue_range = (1, 16)
            red_count = 6
            blue_count = 1
        else:  # DLT
            # 大乐透：红球1-35，蓝球1-12
            red_columns = range(1, 6)  # 第2-6列是红球
            blue_columns = [6, 7]  # 第7-8列是蓝球
            red_range = (1, 35)
            blue_range = (1, 12)
            red_count = 5
            blue_count = 2

        # 统计红球和蓝球的出现次数和概率
        _, red_probs = self.calculate_ball_statistics(
            train_data, red_columns, red_range[0], red_range[1])
        _, blue_probs = self.calculate_ball_statistics(
            train_data, blue_columns, blue_range[0], blue_range[1])

        # 统计跟随性
        _, red_follow_prob = self.calculate_follow_statistics(
            train_data, red_columns, red_range[0], red_range[1])
        _, blue_follow_prob = self.calculate_follow_statistics(
            train_data, blue_columns, blue_range[0], blue_range[1])

        # 获取最新一期号码
        last_numbers = train_data.iloc[-1, 1:].values

        # 新增的AI预测方法
        if method == "bayesian_ai":
            return self.bayesian_prediction_ai(train_data)
        elif method == "markov":
            return self.markov_chain_prediction(train_data)
        elif method == "ensemble":
            return self.ensemble_prediction(train_data)

        # 原有的贝叶斯方法
        if method == "bayesian":
            # 基于贝叶斯概率预测
            red_prediction_probs = np.array([red_probs[i] for i in range(red_range[0], red_range[1] + 1)])
            blue_prediction_probs = np.array([blue_probs[i] for i in range(blue_range[0], blue_range[1] + 1)])

        elif method == "multi_bayesian":
            # 基于多条件贝叶斯概率预测
            red_prediction_probs = np.zeros(red_range[1] - red_range[0] + 1)
            blue_prediction_probs = np.zeros(blue_range[1] - blue_range[0] + 1)

            # 红球多条件贝叶斯概率
            red_probs_list = [red_probs[i] for i in range(red_range[0], red_range[1] + 1)]
            for i in range(red_range[1] - red_range[0] + 1):
                prob_sum = 0
                for j, col in enumerate(red_columns):
                    last_ball = last_numbers[col - 1]  # 获取最新一期对应位置的球号
                    if red_range[0] <= last_ball <= red_range[1]:
                        prob_sum += red_follow_prob[i, last_ball - red_range[0]] * red_probs_list[last_ball - red_range[0]]
                red_prediction_probs[i] = prob_sum

            # 蓝球多条件贝叶斯概率
            blue_probs_list = [blue_probs[i] for i in range(blue_range[0], blue_range[1] + 1)]
            for i in range(blue_range[1] - blue_range[0] + 1):
                prob_sum = 0
                for j, col in enumerate(blue_columns):
                    last_ball = last_numbers[col - 1]  # 获取最新一期对应位置的球号
                    if blue_range[0] <= last_ball <= blue_range[1]:
                        prob_sum += blue_follow_prob[i, last_ball - blue_range[0]] * blue_probs_list[last_ball - blue_range[0]]
                blue_prediction_probs[i] = prob_sum

        else:  # full_bayesian
            # 基于全条件贝叶斯概率预测
            red_prediction_probs = np.zeros(red_range[1] - red_range[0] + 1)
            blue_prediction_probs = np.zeros(blue_range[1] - blue_range[0] + 1)

            # 红球全条件贝叶斯概率
            red_probs_list = [red_probs[i] for i in range(red_range[0], red_range[1] + 1)]
            for i in range(red_range[1] - red_range[0] + 1):
                prob_sum = 0
                for j in range(red_range[1] - red_range[0] + 1):
                    prob_sum += red_follow_prob[i, j] * red_probs_list[j]
                red_prediction_probs[i] = prob_sum

            # 蓝球全条件贝叶斯概率
            blue_probs_list = [blue_probs[i] for i in range(blue_range[0], blue_range[1] + 1)]
            for i in range(blue_range[1] - blue_range[0] + 1):
                prob_sum = 0
                for j in range(blue_range[1] - blue_range[0] + 1):
                    prob_sum += blue_follow_prob[i, j] * blue_probs_list[j]
                blue_prediction_probs[i] = prob_sum

        # 选择概率最高的号码
        red_indices = np.argsort(red_prediction_probs)[-red_count:]
        red_numbers = sorted([i + red_range[0] for i in red_indices])

        blue_indices = np.argsort(blue_prediction_probs)[-blue_count:]
        blue_numbers = sorted([i + blue_range[0] for i in blue_indices])

        return red_numbers + blue_numbers

    def bayesian_prediction_ai(self, train_data):
        """
        基于贝叶斯概率进行预测（AI版本）

        Args:
            train_data: 训练数据

        Returns:
            predicted_numbers: 预测的号码列表
        """
        if self.lottery_type == "SSQ":
            red_columns = range(1, 7)
            blue_columns = [7]
            red_range = (1, 33)
            blue_range = (1, 16)
            red_count = 6
            blue_count = 1
        else:
            red_columns = range(1, 6)
            blue_columns = [6, 7]
            red_range = (1, 35)
            blue_range = (1, 12)
            red_count = 5
            blue_count = 2

        # 计算频率统计
        red_freq, _ = self.calculate_ball_statistics(
            train_data, red_columns, red_range[0], red_range[1])
        blue_freq, _ = self.calculate_ball_statistics(
            train_data, blue_columns, blue_range[0], blue_range[1])

        # 计算趋势分析（最近100期）
        recent_periods = min(100, len(train_data))
        recent_data = train_data.tail(recent_periods)

        red_recent_freq = {i: 0 for i in range(red_range[0], red_range[1] + 1)}
        for col in red_columns:
            for value in recent_data.iloc[:, col]:
                if red_range[0] <= value <= red_range[1]:
                    red_recent_freq[value] += 1

        blue_recent_freq = {i: 0 for i in range(blue_range[0], blue_range[1] + 1)}
        for col in blue_columns:
            for value in recent_data.iloc[:, col]:
                if blue_range[0] <= value <= blue_range[1]:
                    blue_recent_freq[value] += 1

        # 计算红球预测概率
        red_prediction_probs = {}
        total_red_freq = sum(red_freq.values())
        total_red_recent = sum(red_recent_freq.values())

        for ball in range(red_range[0], red_range[1] + 1):
            # 基础概率（历史频率）
            base_prob = red_freq[ball] / total_red_freq if total_red_freq > 0 else 1 / (red_range[1] - red_range[0] + 1)

            # 趋势权重（最近期数的表现）
            trend_weight = red_recent_freq[ball] / total_red_recent if total_red_recent > 0 else 0

            # 综合概率（基础概率 * 0.7 + 趋势权重 * 0.3）
            red_prediction_probs[ball] = base_prob * 0.7 + trend_weight * 0.3

        # 计算蓝球预测概率
        blue_prediction_probs = {}
        total_blue_freq = sum(blue_freq.values())
        total_blue_recent = sum(blue_recent_freq.values())

        for ball in range(blue_range[0], blue_range[1] + 1):
            # 基础概率（历史频率）
            base_prob = blue_freq[ball] / total_blue_freq if total_blue_freq > 0 else 1 / (blue_range[1] - blue_range[0] + 1)

            # 趋势权重（最近期数的表现）
            trend_weight = blue_recent_freq[ball] / total_blue_recent if total_blue_recent > 0 else 0

            # 综合概率（基础概率 * 0.7 + 趋势权重 * 0.3）
            blue_prediction_probs[ball] = base_prob * 0.7 + trend_weight * 0.3

        # 选择概率最高的号码
        red_sorted = sorted(red_prediction_probs.items(), key=lambda x: x[1], reverse=True)
        blue_sorted = sorted(blue_prediction_probs.items(), key=lambda x: x[1], reverse=True)

        predicted_red = [ball for ball, _ in red_sorted[:red_count]]
        predicted_blue = [ball for ball, _ in blue_sorted[:blue_count]]

        return sorted(predicted_red) + sorted(predicted_blue)

    def markov_chain_prediction(self, train_data):
        """
        基于马尔可夫链进行预测

        Args:
            train_data: 训练数据

        Returns:
            predicted_numbers: 预测的号码列表
        """
        if self.lottery_type == "SSQ":
            red_columns = range(1, 7)
            blue_columns = [7]
            red_range = (1, 33)
            blue_range = (1, 16)
            red_count = 6
            blue_count = 1
        else:
            red_columns = range(1, 6)
            blue_columns = [6, 7]
            red_range = (1, 35)
            blue_range = (1, 12)
            red_count = 5
            blue_count = 2

        # 计算频率统计
        red_freq, _ = self.calculate_ball_statistics(
            train_data, red_columns, red_range[0], red_range[1])
        blue_freq, _ = self.calculate_ball_statistics(
            train_data, blue_columns, blue_range[0], blue_range[1])

        # 计算趋势分析（最近100期）
        recent_periods = min(100, len(train_data))
        recent_data = train_data.tail(recent_periods)

        red_recent_freq = {i: 0 for i in range(red_range[0], red_range[1] + 1)}
        for col in red_columns:
            for value in recent_data.iloc[:, col]:
                if red_range[0] <= value <= red_range[1]:
                    red_recent_freq[value] += 1

        blue_recent_freq = {i: 0 for i in range(blue_range[0], blue_range[1] + 1)}
        for col in blue_columns:
            for value in recent_data.iloc[:, col]:
                if blue_range[0] <= value <= blue_range[1]:
                    blue_recent_freq[value] += 1

        # 计算重号分析
        repeat_stats = self.calculate_repeat_analysis()

        # 获取最新一期号码
        if self.lottery_type == "SSQ":
            latest_red = set(train_data.iloc[-1, 1:7].tolist())
            latest_blue = set(train_data.iloc[-1, 7:8].tolist())
        else:
            latest_red = set(train_data.iloc[-1, 1:6].tolist())
            latest_blue = set(train_data.iloc[-1, 6:8].tolist())

        # 计算红球预测概率
        red_prediction_probs = {}
        for ball in range(red_range[0], red_range[1] + 1):
            # 基础频率权重
            base_weight = red_freq[ball] / sum(red_freq.values()) if sum(red_freq.values()) > 0 else 0

            # 趋势权重
            trend_weight = red_recent_freq[ball] / sum(red_recent_freq.values()) if sum(red_recent_freq.values()) > 0 else 0

            # 重号权重（如果上期出现过，降低概率；如果是常见重号，提高概率）
            repeat_weight = 0
            if ball in latest_red:
                # 上期出现过，根据重号统计调整
                repeat_rate = repeat_stats['red_repeat_numbers'][ball] / len(train_data) if len(train_data) > 0 else 0
                repeat_weight = -0.3 + repeat_rate * 0.5  # 基础降权，但常见重号会有补偿
            else:
                # 上期未出现，正常权重
                repeat_weight = 0.1

            # 综合概率
            red_prediction_probs[ball] = base_weight * 0.4 + trend_weight * 0.4 + repeat_weight * 0.2

        # 计算蓝球预测概率
        blue_prediction_probs = {}
        for ball in range(blue_range[0], blue_range[1] + 1):
            # 基础频率权重
            base_weight = blue_freq[ball] / sum(blue_freq.values()) if sum(blue_freq.values()) > 0 else 0

            # 趋势权重
            trend_weight = blue_recent_freq[ball] / sum(blue_recent_freq.values()) if sum(blue_recent_freq.values()) > 0 else 0

            # 重号权重
            repeat_weight = 0
            if ball in latest_blue:
                repeat_rate = repeat_stats['blue_repeat_numbers'][ball] / len(train_data) if len(train_data) > 0 else 0
                repeat_weight = -0.3 + repeat_rate * 0.5
            else:
                repeat_weight = 0.1

            # 综合概率
            blue_prediction_probs[ball] = base_weight * 0.4 + trend_weight * 0.4 + repeat_weight * 0.2

        # 选择概率最高的号码
        red_sorted = sorted(red_prediction_probs.items(), key=lambda x: x[1], reverse=True)
        blue_sorted = sorted(blue_prediction_probs.items(), key=lambda x: x[1], reverse=True)

        predicted_red = [ball for ball, _ in red_sorted[:red_count]]
        predicted_blue = [ball for ball, _ in blue_sorted[:blue_count]]

        return sorted(predicted_red) + sorted(predicted_blue)

    def ensemble_prediction(self, train_data):
        """
        集成预测方法，综合多种预测结果

        Args:
            train_data: 训练数据

        Returns:
            predicted_numbers: 预测的号码列表
        """
        if self.lottery_type == "SSQ":
            red_count = 6
            blue_count = 1
            red_range = (1, 33)
            blue_range = (1, 16)
        else:
            red_count = 5
            blue_count = 2
            red_range = (1, 35)
            blue_range = (1, 12)

        # 获取贝叶斯预测结果
        bayesian_numbers = self.bayesian_prediction_ai(train_data)

        # 获取马尔可夫预测结果
        markov_numbers = self.markov_chain_prediction(train_data)

        # 分离红球和蓝球
        if self.lottery_type == "SSQ":
            bayesian_red = bayesian_numbers[:6]
            bayesian_blue = bayesian_numbers[6:7]
            markov_red = markov_numbers[:6]
            markov_blue = markov_numbers[6:7]
        else:
            bayesian_red = bayesian_numbers[:5]
            bayesian_blue = bayesian_numbers[5:7]
            markov_red = markov_numbers[:5]
            markov_blue = markov_numbers[5:7]

        # 计算集成概率
        red_probs = {i: 0 for i in range(red_range[0], red_range[1] + 1)}
        blue_probs = {i: 0 for i in range(blue_range[0], blue_range[1] + 1)}

        # 红球集成：给预测中的号码加权
        for ball in bayesian_red:
            red_probs[ball] += 0.5  # 贝叶斯权重50%
        for ball in markov_red:
            red_probs[ball] += 0.5  # 马尔可夫权重50%

        # 蓝球集成：给预测中的号码加权
        for ball in bayesian_blue:
            blue_probs[ball] += 0.5  # 贝叶斯权重50%
        for ball in markov_blue:
            blue_probs[ball] += 0.5  # 马尔可夫权重50%

        # 选择概率最高的号码
        red_sorted = sorted(red_probs.items(), key=lambda x: x[1], reverse=True)
        blue_sorted = sorted(blue_probs.items(), key=lambda x: x[1], reverse=True)

        predicted_red = [ball for ball, _ in red_sorted[:red_count]]
        predicted_blue = [ball for ball, _ in blue_sorted[:blue_count]]

        return sorted(predicted_red) + sorted(predicted_blue)

    def check_hit_rate(self, predicted, actual):
        """
        检查预测号码与实际号码的命中情况

        Args:
            predicted: 预测号码列表
            actual: 实际号码列表

        Returns:
            hit_info: 命中信息字典
        """
        if self.lottery_type == "SSQ":
            # 双色球：前6个是红球，第7个是蓝球
            pred_red = set(predicted[:6])
            pred_blue = set(predicted[6:7])
            actual_red = set(actual[:6])
            actual_blue = set(actual[6:7])

            red_hits = len(pred_red & actual_red)
            blue_hits = len(pred_blue & actual_blue)

            # SSQ中奖条件：红球至少5个相等，蓝球1个相等
            is_hit = red_hits >= 5 and blue_hits == 1

        else:  # DLT
            # 大乐透：前5个是红球，后2个是蓝球
            pred_red = set(predicted[:5])
            pred_blue = set(predicted[5:7])
            actual_red = set(actual[:5])
            actual_blue = set(actual[5:7])

            red_hits = len(pred_red & actual_red)
            blue_hits = len(pred_blue & actual_blue)

            # DLT中奖条件：红球至少4个相等，蓝球2个相等
            is_hit = red_hits >= 4 and blue_hits == 2

        return {
            'red_hits': red_hits,
            'blue_hits': blue_hits,
            'total_hits': red_hits + blue_hits,
            'is_hit': is_hit
        }

    def step3_calculate_and_compare(self):
        """
        第三步：开始计算和比对
        修改：比对范围为指定行之后的12期数据，选择命中号码数量最多的期号作为比对结果
        若存在命中数量相同的情况，选择与指定行最近的期号
        """
        print("\n" + "=" * 60)
        print("第三步：开始计算和比对")
        print("=" * 60)

        print(f"开始从第 {self.start_row} 行进行计算...")
        print(f"使用方法: {self.method}")
        print(f"将进行最多500次计算和比对...")
        print(f"每次预测将与后续12期数据进行比对，选择命中数量最多的期号...")
        print(f"若命中数量相同，则选择与指定行最近的期号...")

        self.results = []
        hit_count = 0

        for i in range(500):
            current_row = self.start_row + i

            # 检查是否超出数据范围（需要确保后面还有12期数据可以比对）
            if current_row + 12 >= len(self.data):
                print(f"警告: 第 {current_row} 行后续数据不足12期，停止计算")
                break

            # 获取训练数据（从开始到当前行）
            train_data = self.data.iloc[:current_row].copy()

            # 预测下一期号码
            predicted_numbers = self.predict_numbers(train_data, self.method)

            if predicted_numbers is None:
                print(f"第 {i+1} 次计算失败：训练数据不足")
                continue

            # 与后续12期数据进行比对，找出命中号码数量最多的期号
            best_hit_info = None
            best_target_period = None
            best_actual_numbers = None
            max_hit_count = -1
            closest_period_index = None  # 记录最近期号的索引

            # 遍历后续12期数据，找出命中数量最多的期号
            for j in range(1, 13):  # 检查后续12期数据（指定行+1到指定行+12）
                target_row = current_row + j
                actual_numbers = self.data.iloc[target_row, 1:].values.tolist()

                # 检查命中情况
                hit_info = self.check_hit_rate(predicted_numbers, actual_numbers)
                current_hit_count = hit_info['total_hits']

                # 如果当前期号的命中数量更多，或者命中数量相同但期号更近，则更新最佳结果
                if (current_hit_count > max_hit_count or
                    (current_hit_count == max_hit_count and (closest_period_index is None or j < closest_period_index))):
                    max_hit_count = current_hit_count
                    best_hit_info = hit_info
                    best_target_period = self.data.iloc[target_row, 0]
                    best_actual_numbers = actual_numbers
                    closest_period_index = j

            # 如果没有找到任何比对结果（理论上不会发生），使用第一期作为默认
            if best_hit_info is None:
                target_row = current_row + 1
                actual_numbers = self.data.iloc[target_row, 1:].values.tolist()
                hit_info = self.check_hit_rate(predicted_numbers, actual_numbers)
                best_target_period = self.data.iloc[target_row, 0]
                best_actual_numbers = actual_numbers
                best_hit_info = hit_info

            # 记录结果
            result = {
                'iteration': i + 1,
                'base_period': self.data.iloc[current_row - 1, 0],  # 基于的期号
                'target_period': best_target_period,    # 比对的期号
                'method': self.method,
                'predicted_numbers': predicted_numbers,
                'actual_numbers': best_actual_numbers,
                'red_hits': best_hit_info['red_hits'],
                'blue_hits': best_hit_info['blue_hits'],
                'total_hits': best_hit_info['total_hits'],
                'is_hit': best_hit_info['is_hit'],
                'hit_in_12_periods': best_hit_info['is_hit'],  # 标记是否在12期内命中
                'max_hit_count': max_hit_count,  # 记录最大命中数量
                'closest_period_index': closest_period_index  # 记录最近期号的索引
            }

            self.results.append(result)

            # 如果命中，打印结果
            if best_hit_info['is_hit']:
                hit_count += 1
                print(f"命中！第 {i+1} 次计算:")
                print(f"  基于第 {result['base_period']} 期号码")
                print(f"  用 {self.method} 方法预测")
                print(f"  与第 {result['target_period']} 期号码命中")
                print(f"  命中 {best_hit_info['total_hits']} 个号码")
                print(f"  (红球: {best_hit_info['red_hits']}, 蓝球: {best_hit_info['blue_hits']})")
            else:
                # 即使没有达到中奖标准，也显示最佳匹配结果
                print(f"第 {i+1} 次计算:")
                print(f"  基于第 {result['base_period']} 期号码")
                print(f"  用 {self.method} 方法预测")
                print(f"  最佳匹配：第 {result['target_period']} 期，命中 {best_hit_info['total_hits']} 个号码")
                print(f"  (红球: {best_hit_info['red_hits']}, 蓝球: {best_hit_info['blue_hits']})")

            # 显示进度
            if (i + 1) % 50 == 0:
                print(f"已完成 {i + 1}/500 次计算，当前命中次数: {hit_count}")

        print(f"\n第三步完成！")
        print(f"总计算次数: {len(self.results)}")
        print(f"总命中次数: {hit_count}")
        print(f"命中率: {hit_count/len(self.results)*100:.2f}%" if self.results else "0%")

        return True

    def step4_save_results_to_excel(self):
        """
        第四步：将校核结果保存在Excel文件中
        """
        print("\n" + "=" * 60)
        print("第四步：将校核结果保存在Excel文件中")
        print("=" * 60)

        if not self.results:
            print("没有结果数据可保存")
            return False

        try:
            # 创建结果DataFrame
            results_data = []
            for result in self.results:
                row = {
                    '计算次序': result['iteration'],
                    '基于期号': result['base_period'],
                    '比对期号': result['target_period'],
                    '计算方法': result['method'],
                    '预测号码': str(result['predicted_numbers']),
                    '实际号码': str(result['actual_numbers']),
                    '红球命中数': result['red_hits'],
                    '蓝球命中数': result['blue_hits'],
                    '总命中数': result['total_hits'],
                    '是否命中': '是' if result['is_hit'] else '否',
                    '12期内命中': '是' if result.get('hit_in_12_periods', False) else '否',
                    '最大命中数': result.get('max_hit_count', result['total_hits']),
                    '期号距离': result.get('closest_period_index', 1)
                }
                results_data.append(row)

            results_df = pd.DataFrame(results_data)

            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"lottery_analysis_results_{self.lottery_type}_{timestamp}.xlsx"

            # 保存到Excel文件
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                # 保存详细结果
                results_df.to_excel(writer, sheet_name='详细结果', index=False)

                # 创建汇总统计
                total_calculations = len(self.results)
                total_hits = sum(1 for r in self.results if r['is_hit'])
                total_hits_in_12 = sum(1 for r in self.results if r.get('hit_in_12_periods', False))
                hit_rate = total_hits / total_calculations * 100 if total_calculations > 0 else 0
                hit_rate_in_12 = total_hits_in_12 / total_calculations * 100 if total_calculations > 0 else 0

                summary_data = {
                    '统计项目': ['彩票类型', '计算方法', '开始行数', '总计算次数', '总命中次数', '命中率(%)', '12期内命中次数', '12期内命中率(%)'],
                    '数值': [self.lottery_type, self.method, self.start_row,
                            total_calculations, total_hits, f"{hit_rate:.2f}", total_hits_in_12, f"{hit_rate_in_12:.2f}"]
                }
                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name='汇总统计', index=False)

                # 保存命中详情
                hit_results = [r for r in self.results if r['is_hit']]
                if hit_results:
                    hit_data = []
                    for result in hit_results:
                        row = {
                            '计算次序': result['iteration'],
                            '基于期号': result['base_period'],
                            '比对期号': result['target_period'],
                            '预测号码': str(result['predicted_numbers']),
                            '实际号码': str(result['actual_numbers']),
                            '命中详情': f"红球{result['red_hits']}个，蓝球{result['blue_hits']}个",
                            '12期内命中': '是' if result.get('hit_in_12_periods', False) else '否'
                        }
                        hit_data.append(row)

                    hit_df = pd.DataFrame(hit_data)
                    hit_df.to_excel(writer, sheet_name='命中详情', index=False)

            print(f"结果已保存到文件: {filename}")
            print(f"包含工作表: 详细结果、汇总统计" + ("、命中详情" if hit_results else ""))

            return True

        except Exception as e:
            print(f"保存结果时出错: {str(e)}")
            return False

    def run_analysis(self):
        """
        运行完整的四步分析流程
        """
        print("开始彩票数据分析四步程序")
        print("=" * 60)

        # 第一步：读取数据
        if not self.step1_read_and_sort_data():
            return False

        # 第二步：获取用户参数
        if not self.step2_get_user_parameters():
            return False

        # 第三步：计算和比对
        if not self.step3_calculate_and_compare():
            return False

        # 第四步：保存结果
        if not self.step4_save_results_to_excel():
            return False

        print("\n" + "=" * 60)
        print("四步分析流程全部完成！")
        print("=" * 60)

        return True

if __name__ == "__main__":
    # 创建分析器实例
    analyzer = LotteryAnalyzer()

    # 运行完整分析流程
    analyzer.run_analysis()
